"""
文件操作工具集
"""
import os
import shutil
from pathlib import Path
from typing import Dict, Any
from mcp.server.fastmcp import FastMCP
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tool_registry import tool

# 创建MCP服务器实例
mcp = FastMCP("file_operations")

@mcp.tool()
@tool("读取文件内容")
def read_file(file_path: str) -> Dict[str, Any]:
    """读取指定文件的内容

    Args:
        file_path: 要读取的文件路径

    Returns:
        包含文件内容的字典
    """
    try:
        if not os.path.exists(file_path):
            return {"success": False, "error": f"File not found: {file_path}"}

        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        return {
            "success": True,
            "file_path": file_path,
            "content": content,
            "size": len(content)
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
@tool("写入文件内容")
def write_file(file_path: str, content: str, mode: str = "w") -> Dict[str, Any]:
    """写入内容到指定文件

    Args:
        file_path: 目标文件路径
        content: 要写入的内容
        mode: 写入模式 ('w' 覆盖, 'a' 追加)

    Returns:
        操作结果字典
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)

        with open(file_path, mode, encoding='utf-8') as f:
            f.write(content)

        return {
            "success": True,
            "file_path": file_path,
            "bytes_written": len(content.encode('utf-8'))
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
@tool("列出目录内容")
def list_directory(dir_path: str, show_hidden: bool = False) -> Dict[str, Any]:
    """列出指定目录的内容

    Args:
        dir_path: 目录路径
        show_hidden: 是否显示隐藏文件

    Returns:
        目录内容列表
    """
    try:
        if not os.path.exists(dir_path):
            return {"success": False, "error": f"Directory not found: {dir_path}"}

        if not os.path.isdir(dir_path):
            return {"success": False, "error": f"Path is not a directory: {dir_path}"}

        items = []
        for item in os.listdir(dir_path):
            if not show_hidden and item.startswith('.'):
                continue

            item_path = os.path.join(dir_path, item)
            item_info = {
                "name": item,
                "path": item_path,
                "is_file": os.path.isfile(item_path),
                "is_directory": os.path.isdir(item_path),
                "size": os.path.getsize(item_path) if os.path.isfile(item_path) else 0
            }
            items.append(item_info)

        return {
            "success": True,
            "directory": dir_path,
            "items": items,
            "count": len(items)
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
@tool("删除文件或目录")
def delete_path(path: str, recursive: bool = False) -> Dict[str, Any]:
    """删除指定的文件或目录

    Args:
        path: 要删除的路径
        recursive: 是否递归删除目录

    Returns:
        删除操作结果
    """
    try:
        if not os.path.exists(path):
            return {"success": False, "error": f"Path not found: {path}"}

        if os.path.isfile(path):
            os.remove(path)
            return {"success": True, "action": "file_deleted", "path": path}
        elif os.path.isdir(path):
            if recursive:
                shutil.rmtree(path)
                return {"success": True, "action": "directory_deleted", "path": path}
            else:
                os.rmdir(path)  # 只删除空目录
                return {"success": True, "action": "empty_directory_deleted", "path": path}
        else:
            return {"success": False, "error": f"Unknown path type: {path}"}

    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == '__main__':
    mcp.run(transport="stdio")
