"""
系统信息工具集
"""
import os
import platform
import psutil
import datetime
from typing import Dict, Any
from mcp.server.fastmcp import FastMCP
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tool_registry import tool

# 创建MCP服务器实例
mcp = FastMCP("system_info")

@mcp.tool()
@tool("获取系统基本信息")
def get_system_info() -> Dict[str, Any]:
    """获取系统基本信息

    Returns:
        系统信息字典
    """
    try:
        return {
            "success": True,
            "system": platform.system(),
            "platform": platform.platform(),
            "architecture": platform.architecture(),
            "processor": platform.processor(),
            "python_version": platform.python_version(),
            "hostname": platform.node(),
            "current_time": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
@tool("获取CPU信息")
def get_cpu_info() -> Dict[str, Any]:
    """获取CPU使用信息

    Returns:
        CPU信息字典
    """
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        cpu_count = psutil.cpu_count()
        cpu_freq = psutil.cpu_freq()

        return {
            "success": True,
            "cpu_percent": cpu_percent,
            "cpu_count_logical": cpu_count,
            "cpu_count_physical": psutil.cpu_count(logical=False),
            "cpu_frequency": {
                "current": cpu_freq.current if cpu_freq else None,
                "min": cpu_freq.min if cpu_freq else None,
                "max": cpu_freq.max if cpu_freq else None
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
@tool("获取内存信息")
def get_memory_info() -> Dict[str, Any]:
    """获取内存使用信息

    Returns:
        内存信息字典
    """
    try:
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()

        return {
            "success": True,
            "virtual_memory": {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "free": memory.free,
                "percent": memory.percent
            },
            "swap_memory": {
                "total": swap.total,
                "used": swap.used,
                "free": swap.free,
                "percent": swap.percent
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
@tool("获取磁盘信息")
def get_disk_info() -> Dict[str, Any]:
    """获取磁盘使用信息

    Returns:
        磁盘信息字典
    """
    try:
        disk_partitions = []
        for partition in psutil.disk_partitions():
            try:
                usage = psutil.disk_usage(partition.mountpoint)
                disk_partitions.append({
                    "device": partition.device,
                    "mountpoint": partition.mountpoint,
                    "fstype": partition.fstype,
                    "total": usage.total,
                    "used": usage.used,
                    "free": usage.free,
                    "percent": (usage.used / usage.total) * 100
                })
            except PermissionError:
                continue

        return {
            "success": True,
            "disk_partitions": disk_partitions
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
@tool("获取网络信息")
def get_network_info() -> Dict[str, Any]:
    """获取网络接口信息

    Returns:
        网络信息字典
    """
    try:
        network_interfaces = {}
        for interface, addresses in psutil.net_if_addrs().items():
            interface_info = []
            for addr in addresses:
                interface_info.append({
                    "family": str(addr.family),
                    "address": addr.address,
                    "netmask": addr.netmask,
                    "broadcast": addr.broadcast
                })
            network_interfaces[interface] = interface_info

        network_stats = psutil.net_io_counters()

        return {
            "success": True,
            "interfaces": network_interfaces,
            "io_counters": {
                "bytes_sent": network_stats.bytes_sent,
                "bytes_recv": network_stats.bytes_recv,
                "packets_sent": network_stats.packets_sent,
                "packets_recv": network_stats.packets_recv
            }
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

@mcp.tool()
@tool("获取运行进程信息")
def get_process_info(limit: int = 10) -> Dict[str, Any]:
    """获取运行中的进程信息

    Args:
        limit: 返回进程数量限制

    Returns:
        进程信息字典
    """
    try:
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
            try:
                processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue

        # 按CPU使用率排序
        processes.sort(key=lambda x: x['cpu_percent'] or 0, reverse=True)

        return {
            "success": True,
            "processes": processes[:limit],
            "total_processes": len(processes)
        }
    except Exception as e:
        return {"success": False, "error": str(e)}

if __name__ == '__main__':
    mcp.run(transport="stdio")
