"""
工具模板 - 复制此文件来创建新的工具集
"""
from mcp.server.fastmcp import FastMCP
from typing import Dict, Any
import sys
import os

# 添加父目录到路径以导入 tool_registry
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tool_registry import tool

# 创建MCP服务器实例 - 修改为您的工具集名称
mcp = FastMCP("tool_template")

@mcp.tool()
@tool("示例工具 - 问候用户")
def greet_user(name: str, language: str = "zh") -> Dict[str, Any]:
    """向用户问候
    
    Args:
        name: 用户姓名
        language: 语言代码 (zh/en)
        
    Returns:
        包含问候信息的字典
    """
    greetings = {
        "zh": f"你好，{name}！",
        "en": f"Hello, {name}!"
    }
    
    greeting = greetings.get(language, greetings["zh"])
    
    return {
        "success": True,
        "greeting": greeting,
        "language": language,
        "user_name": name
    }

@mcp.tool()
@tool("示例工具 - 计算圆的面积")
def calculate_circle_area(radius: float) -> Dict[str, Any]:
    """计算圆的面积
    
    Args:
        radius: 圆的半径
        
    Returns:
        包含计算结果的字典
    """
    import math
    
    if radius < 0:
        return {
            "success": False,
            "error": "半径不能为负数"
        }
    
    area = math.pi * radius * radius
    
    return {
        "success": True,
        "radius": radius,
        "area": round(area, 2),
        "formula": "π × r²"
    }

@mcp.tool()
@tool("示例工具 - 生成随机数")
def generate_random_number(min_val: int = 1, max_val: int = 100) -> Dict[str, Any]:
    """生成指定范围内的随机数
    
    Args:
        min_val: 最小值
        max_val: 最大值
        
    Returns:
        包含随机数的字典
    """
    import random
    
    if min_val > max_val:
        return {
            "success": False,
            "error": "最小值不能大于最大值"
        }
    
    random_num = random.randint(min_val, max_val)
    
    return {
        "success": True,
        "random_number": random_num,
        "range": f"{min_val} - {max_val}"
    }

@mcp.tool()
@tool("示例工具 - 文本统计")
def analyze_text(text: str) -> Dict[str, Any]:
    """分析文本的基本统计信息
    
    Args:
        text: 要分析的文本
        
    Returns:
        包含文本统计信息的字典
    """
    if not text:
        return {
            "success": False,
            "error": "文本不能为空"
        }
    
    # 基本统计
    char_count = len(text)
    word_count = len(text.split())
    line_count = len(text.split('\n'))
    
    # 字符频率统计
    char_freq = {}
    for char in text.lower():
        if char.isalpha():
            char_freq[char] = char_freq.get(char, 0) + 1
    
    # 最常见的字符
    most_common_char = max(char_freq.items(), key=lambda x: x[1]) if char_freq else None
    
    return {
        "success": True,
        "statistics": {
            "character_count": char_count,
            "word_count": word_count,
            "line_count": line_count,
            "most_common_character": most_common_char[0] if most_common_char else None,
            "most_common_frequency": most_common_char[1] if most_common_char else 0
        },
        "character_frequency": dict(sorted(char_freq.items(), key=lambda x: x[1], reverse=True)[:5])
    }

# 如果直接运行此文件，启动MCP服务器
if __name__ == '__main__':
    mcp.run(transport="stdio")

"""
使用此模板创建新工具的步骤：

1. 复制此文件到 tools/ 目录，重命名为您的工具集名称
   例如: cp tools/tool_template.py tools/my_awesome_tools.py

2. 修改服务器名称:
   mcp = FastMCP("my_awesome_tools")

3. 删除示例函数，添加您自己的工具函数

4. 每个工具函数需要:
   - 使用 @mcp.tool() 装饰器
   - 使用 @tool("描述") 装饰器
   - 有清晰的文档字符串
   - 返回字典格式的结果
   - 包含 success 字段表示执行状态

5. 重启客户端，新工具会自动被发现

工具函数最佳实践：
- 参数类型注解清晰
- 错误处理完善
- 返回格式统一
- 文档字符串详细
- 功能单一明确
"""
